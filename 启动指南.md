# 自然商城 - 启动指南

## 🚀 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动开发服务器
```bash
npm run dev
```

### 3. 访问应用
- 前台页面：http://localhost:3000
- 管理后台：http://localhost:3000/admin

## 📋 测试账户

### 普通用户
- 邮箱：<EMAIL>
- 密码：123456

### 管理员
- 邮箱：<EMAIL>
- 密码：123456

## 🎯 主要功能

### 前台功能
- ✅ 用户注册/登录
- ✅ 商品浏览和搜索
- ✅ 购物车管理
- ✅ 订单管理
- ✅ 个人中心
- ✅ 收藏功能

### 后台功能
- ✅ 数据概览
- ✅ 商品管理
- ✅ 订单管理
- ✅ 用户管理

## 🎨 设计特色

### 自然柔和风格
- 背景色：淡米色 (#f5f5f1)
- 主要文字：深绿色 (#4a593d)
- 标题文字：中等绿色 (#6e8b67)
- 卡片背景：奶白色 (#fffcf6)
- 边框颜色：淡棕色 (#e4ddd3)
- 按钮颜色：淡绿色 (#a9c3a6)

### 圆润设计
- 组件圆角：15px
- 按钮圆角：30px
- 轻柔阴影效果

## 📱 响应式设计

项目完全支持响应式设计：
- 桌面端 (>= 1200px)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🛠️ 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite
- **UI 组件库**：Element Plus
- **图标库**：@element-plus/icons-vue
- **路由管理**：Vue Router 4
- **状态管理**：Pinia
- **HTTP 客户端**：Axios
- **日期处理**：Day.js
- **样式预处理**：SCSS

## 📁 项目结构

```
src/
├── api/                 # API 接口
├── components/          # 公共组件
│   ├── common/         # 通用组件
│   ├── layout/         # 布局组件
│   └── product/        # 商品相关组件
├── router/             # 路由配置
├── stores/             # Pinia 状态管理
├── styles/             # 全局样式
├── utils/              # 工具函数
├── views/              # 页面组件
│   ├── admin/          # 管理后台
│   ├── auth/           # 认证相关
│   ├── cart/           # 购物车
│   ├── order/          # 订单
│   ├── product/        # 商品
│   ├── search/         # 搜索
│   └── user/           # 用户中心
└── main.js             # 应用入口
```

## 🔧 开发命令

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 📝 注意事项

1. **模拟数据**：当前使用模拟数据，实际项目中需要连接真实的后端 API
2. **图片资源**：使用 Unsplash 在线图片，实际项目中应使用本地或 CDN 图片
3. **支付功能**：仅为演示，实际项目中需要集成真实的支付接口
4. **权限控制**：基础的路由守卫，实际项目中需要更完善的权限系统

## 🌟 特色亮点

1. **自然柔和的设计风格**：温馨舒适的视觉体验
2. **完整的电商功能**：从浏览到购买的完整流程
3. **响应式设计**：完美适配各种设备
4. **现代化技术栈**：Vue 3 + Vite + Element Plus
5. **组件化开发**：高度可复用的组件设计
6. **状态管理**：使用 Pinia 进行状态管理
7. **代码规范**：清晰的代码结构和注释

## 🚀 部署建议

### 开发环境
- 使用 `npm run dev` 启动开发服务器
- 支持热重载和快速开发

### 生产环境
- 使用 `npm run build` 构建生产版本
- 部署到 Nginx、Apache 或其他静态文件服务器
- 建议使用 CDN 加速静态资源

## 📞 技术支持

如有问题，请检查：
1. Node.js 版本是否 >= 16.0.0
2. 依赖是否正确安装
3. 端口 3000 是否被占用

祝您使用愉快！🎉
