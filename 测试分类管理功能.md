# 分类管理功能测试指南

## 功能概述

已为后台管理系统添加了完整的分类管理功能，包括：

### 🎯 **核心功能**
- ✅ 分类列表展示（支持树形结构）
- ✅ 分类创建和编辑
- ✅ 分类删除（单个/批量）
- ✅ 分类状态管理（启用/禁用）
- ✅ 分类排序调整
- ✅ 分类图片上传
- ✅ 分类搜索和筛选

### 📁 **新增文件**
1. `src/api/category.js` - 分类相关API接口
2. `src/views/admin/Categories.vue` - 分类管理页面
3. `src/components/admin/CategoryDialog.vue` - 分类编辑对话框
4. `src/utils/date.js` - 日期格式化工具

### 🔧 **修改文件**
1. `src/router/index.js` - 添加分类管理路由
2. `src/views/admin/AdminLayout.vue` - 添加分类管理菜单

## 测试步骤

### 1. 访问分类管理页面
```
http://localhost:3000/admin/categories
```

### 2. 测试功能点

#### 2.1 查看分类列表
- [ ] 页面正常加载
- [ ] 表格显示分类数据
- [ ] 树形结构正确展示
- [ ] 分页功能正常

#### 2.2 创建新分类
- [ ] 点击"新增分类"按钮
- [ ] 填写分类信息
- [ ] 上传分类图片
- [ ] 选择父级分类
- [ ] 保存成功

#### 2.3 编辑分类
- [ ] 点击"编辑"按钮
- [ ] 修改分类信息
- [ ] 更新成功

#### 2.4 删除分类
- [ ] 单个删除功能
- [ ] 批量删除功能
- [ ] 确认对话框正常

#### 2.5 状态管理
- [ ] 启用/禁用切换
- [ ] 状态更新成功

#### 2.6 排序调整
- [ ] 修改排序数值
- [ ] 排序更新成功

#### 2.7 搜索筛选
- [ ] 按名称搜索
- [ ] 按状态筛选
- [ ] 按层级筛选
- [ ] 重置筛选条件

## API 接口说明

### 管理员分类接口
```javascript
// 获取分类列表
GET /admin/categories?page=1&pageSize=20&keyword=&status=&level=

// 获取分类详情
GET /admin/categories/{id}

// 创建分类
POST /admin/categories
{
  "parent_id": 0,
  "name": "分类名称",
  "description": "分类描述",
  "image": "图片URL",
  "sort": 0,
  "status": "active"
}

// 更新分类
PUT /admin/categories/{id}

// 删除分类
DELETE /admin/categories/{id}

// 批量删除
POST /admin/categories/batch-delete
{
  "ids": [1, 2, 3]
}

// 更新状态
PUT /admin/categories/{id}/status
{
  "status": "active"
}

// 更新排序
PUT /admin/categories/{id}/sort
{
  "sort": 100
}

// 获取分类树
GET /admin/categories/tree

// 移动分类
PUT /admin/categories/{id}/move
{
  "parentId": 2
}
```

### 图片上传接口
```javascript
// 上传图片
POST /upload/image
Content-Type: multipart/form-data
```

## 数据结构

### 分类数据模型
```javascript
{
  "id": 1,
  "parent_id": 0,
  "name": "分类名称",
  "description": "分类描述",
  "image": "图片URL",
  "level": 1,
  "sort": 0,
  "status": "active",
  "created_at": "2024-01-01 12:00:00",
  "updated_at": "2024-01-01 12:00:00",
  "children": []
}
```

## 注意事项

### 🔒 **权限要求**
- 需要管理员权限才能访问
- 需要登录状态

### 🎨 **UI/UX 特性**
- 响应式设计，支持移动端
- 树形表格展示层级关系
- 拖拽上传图片
- 实时搜索和筛选
- 批量操作支持

### 🚀 **性能优化**
- 分页加载数据
- 图片懒加载
- 防抖搜索
- 组件按需加载

### 🛠️ **开发建议**
1. 后端需要实现对应的API接口
2. 确保图片上传功能正常
3. 数据库表结构需要支持树形结构
4. 建议添加分类商品数量统计

## 故障排除

### 常见问题
1. **页面无法访问** - 检查路由配置和权限
2. **API请求失败** - 检查后端接口是否实现
3. **图片上传失败** - 检查上传接口和文件大小限制
4. **样式显示异常** - 检查CSS变量和响应式断点

### 调试方法
1. 打开浏览器开发者工具
2. 查看Network标签页的API请求
3. 查看Console标签页的错误信息
4. 检查Vue DevTools组件状态
