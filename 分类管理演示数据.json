{"categories": [{"id": 1, "parent_id": 0, "name": "生鲜食品", "description": "新鲜健康的生鲜食品", "image": "https://images.unsplash.com/photo-1542838132-92c53300491e?w=300&h=200&fit=crop", "level": 1, "sort": 1, "status": "active", "created_at": "2024-01-01 10:00:00", "updated_at": "2024-01-01 10:00:00", "children": [{"id": 2, "parent_id": 1, "name": "水果", "description": "新鲜水果", "image": "https://images.unsplash.com/photo-1619566636858-adf3ef46400b?w=300&h=200&fit=crop", "level": 2, "sort": 1, "status": "active", "created_at": "2024-01-01 10:05:00", "updated_at": "2024-01-01 10:05:00", "children": [{"id": 8, "parent_id": 2, "name": "苹果", "description": "各种品种的苹果", "image": "https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=300&h=200&fit=crop", "level": 3, "sort": 1, "status": "active", "created_at": "2024-01-01 10:10:00", "updated_at": "2024-01-01 10:10:00", "children": []}, {"id": 9, "parent_id": 2, "name": "香蕉", "description": "进口优质香蕉", "image": "https://images.unsplash.com/photo-1603833665858-e61d17a86224?w=300&h=200&fit=crop", "level": 3, "sort": 2, "status": "active", "created_at": "2024-01-01 10:15:00", "updated_at": "2024-01-01 10:15:00", "children": []}]}, {"id": 3, "parent_id": 1, "name": "蔬菜", "description": "新鲜蔬菜", "image": "https://images.unsplash.com/photo-1540420773420-3366772f4999?w=300&h=200&fit=crop", "level": 2, "sort": 2, "status": "active", "created_at": "2024-01-01 10:06:00", "updated_at": "2024-01-01 10:06:00", "children": [{"id": 10, "parent_id": 3, "name": "叶菜类", "description": "各种叶菜", "image": "https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=300&h=200&fit=crop", "level": 3, "sort": 1, "status": "active", "created_at": "2024-01-01 10:20:00", "updated_at": "2024-01-01 10:20:00", "children": []}]}, {"id": 4, "parent_id": 1, "name": "肉类", "description": "新鲜肉类", "image": "https://images.unsplash.com/photo-1529692236671-f1f6cf9683ba?w=300&h=200&fit=crop", "level": 2, "sort": 3, "status": "active", "created_at": "2024-01-01 10:07:00", "updated_at": "2024-01-01 10:07:00", "children": []}]}, {"id": 5, "parent_id": 0, "name": "日用百货", "description": "日常生活用品", "image": "https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=300&h=200&fit=crop", "level": 1, "sort": 2, "status": "active", "created_at": "2024-01-01 10:01:00", "updated_at": "2024-01-01 10:01:00", "children": [{"id": 6, "parent_id": 5, "name": "清洁用品", "description": "家庭清洁用品", "image": "https://images.unsplash.com/photo-1563453392212-326f5e854473?w=300&h=200&fit=crop", "level": 2, "sort": 1, "status": "active", "created_at": "2024-01-01 10:08:00", "updated_at": "2024-01-01 10:08:00", "children": []}, {"id": 7, "parent_id": 5, "name": "个人护理", "description": "个人护理用品", "image": "https://images.unsplash.com/photo-1556228720-195a672e8a03?w=300&h=200&fit=crop", "level": 2, "sort": 2, "status": "active", "created_at": "2024-01-01 10:09:00", "updated_at": "2024-01-01 10:09:00", "children": []}]}, {"id": 11, "parent_id": 0, "name": "数码电器", "description": "数码产品和家用电器", "image": "https://images.unsplash.com/photo-1498049794561-7780e7231661?w=300&h=200&fit=crop", "level": 1, "sort": 3, "status": "inactive", "created_at": "2024-01-01 10:02:00", "updated_at": "2024-01-01 10:02:00", "children": []}], "pagination": {"current_page": 1, "page_size": 20, "total": 11, "total_pages": 1}, "api_response_format": {"success_response": {"code": 200, "message": "获取成功", "data": {"list": "categories数组", "total": 11, "page": 1, "pageSize": 20}}, "error_response": {"code": 400, "message": "请求参数错误", "data": null}}, "create_category_example": {"request": {"parent_id": 1, "name": "海鲜", "description": "新鲜海鲜产品", "image": "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=200&fit=crop", "sort": 4, "status": "active"}, "response": {"code": 200, "message": "创建成功", "data": {"id": 12, "parent_id": 1, "name": "海鲜", "description": "新鲜海鲜产品", "image": "https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=300&h=200&fit=crop", "level": 2, "sort": 4, "status": "active", "created_at": "2024-01-01 12:00:00", "updated_at": "2024-01-01 12:00:00"}}}, "upload_image_example": {"request": "multipart/form-data with 'image' field", "response": {"code": 200, "message": "上传成功", "data": {"url": "https://example.com/uploads/images/20240101/abc123.jpg", "filename": "abc123.jpg", "originalName": "category.jpg", "size": 102400, "mimeType": "image/jpeg"}}}}