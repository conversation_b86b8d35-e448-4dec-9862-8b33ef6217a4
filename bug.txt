PS D:\office\cursor\电商平台> npm run dev

> ecommerce-platform@1.0.0 dev
> vite


  VITE v5.4.19  ready in 525 ms

  ➜  Local:   http://localhost:3000/
  ➜  Network: use --host to expose
  ➜  press h + enter to show help
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
9 │ @import './reset.scss';
  │         ^^^^^^^^^^^^^^
  ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 9:9  root stylesheet     

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
10 │ @import './element-plus.scss';
   │         ^^^^^^^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 10:9  root stylesheet    

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │ @import './common.scss';
   │         ^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 11:9  root stylesheet    

Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
19:59:27 [vite] ✨ new dependencies optimized: element-plus/es/components/carousel/style/css, element-plus/es/components/carousel-item/style/css, element-plus/es/components/tag/style/css, element-plus/es/components/rate/style/css, dayjs
19:59:27 [vite] ✨ optimized dependencies changed. reloading
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
9 │ @import './reset.scss';
  │         ^^^^^^^^^^^^^^
  ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 9:9  root stylesheet     

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
10 │ @import './element-plus.scss';
   │         ^^^^^^^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 10:9  root stylesheet    

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │ @import './common.scss';
   │         ^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 11:9  root stylesheet    

Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
19:59:34 [vite] ✨ new dependencies optimized: element-plus/es/components/dialog/style/css, element-plus/es/components/form/style/css, element-plus/es/components/link/style/css, element-plus/es/components/checkbox/style/css, element-plus/es/components/form-item/style/css
19:59:34 [vite] ✨ optimized dependencies changed. reloading
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
9 │ @import './reset.scss';
  │         ^^^^^^^^^^^^^^
  ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 9:9  root stylesheet     

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
10 │ @import './element-plus.scss';
   │         ^^^^^^^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 10:9  root stylesheet    

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │ @import './common.scss';
   │         ^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 11:9  root stylesheet    

Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
19:59:37 [vite] ✨ new dependencies optimized: element-plus/es/components/loading/style/css, element-plus/es/components/pagination/style/css, element-plus/es/components/empty/style/css, element-plus/es/components/button-group/style/css, element-plus/es/components/select/style/css, element-plus/es/components/option/style/css, element-plus/es/components/breadcrumb/style/css, element-plus/es/components/breadcrumb-item/style/css, element-plus/es/components/input-number/style/css
19:59:37 [vite] ✨ optimized dependencies changed. reloading
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

  ╷
9 │ @import './reset.scss';
  │         ^^^^^^^^^^^^^^
  ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 9:9  root stylesheet     

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
10 │ @import './element-plus.scss';
   │         ^^^^^^^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 10:9  root stylesheet    

Deprecation Warning [import]: Sass @import rules are deprecated and will be removed in Dart Sass 3.0.0.

More info and automated migrator: https://sass-lang.com/d/import

   ╷
11 │ @import './common.scss';
   │         ^^^^^^^^^^^^^^^
   ╵
    file:///D:/office/cursor/%E7%94%B5%E5%95%86%E5%B9%B3%E5%8F%B0/src/styles/index.scss 11:9  root stylesheet    

Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
Deprecation [legacy-js-api]: The legacy JS API is deprecated and will be removed in Dart Sass 2.0.0.

More info: https://sass-lang.com/d/legacy-js-api
PS D:\office\cursor\电商平台>