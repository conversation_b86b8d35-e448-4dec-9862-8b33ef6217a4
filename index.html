<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="自然柔和风格的电商平台" />
    <meta name="keywords" content="电商,购物,商城,自然,柔和" />
    <title>自然商城 - 电商平台</title>
    <style>
      /* 预加载样式，防止闪烁 */
      body {
        margin: 0;
        padding: 0;
        background-color: #f5f5f1;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      }
      
      #app {
        min-height: 100vh;
      }
      
      /* 加载动画 */
      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: #f5f5f1;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #e4ddd3;
        border-top: 3px solid #6e8b67;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="loading">
        <div class="loading-spinner"></div>
      </div>
    </div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
