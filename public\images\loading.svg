<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="shimmer" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#f0f0f0;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#e0e0e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f0f0f0;stop-opacity:1" />
      <animateTransform
        attributeName="gradientTransform"
        type="translate"
        values="-200 0;200 0;-200 0"
        dur="2s"
        repeatCount="indefinite"/>
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#shimmer)" rx="8"/>
  <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#999" font-family="Arial, sans-serif" font-size="14">
    加载中...
  </text>
</svg>
