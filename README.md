# 自然商城 - 电商平台

一个基于 Vue 3 + Vite + Element Plus 的现代化电商平台前端项目，采用自然柔和的设计风格。

## 🌿 设计特色

- **自然柔和风格**：采用淡米色背景、深绿色文字、奶白色卡片的温馨配色
- **圆润设计**：15px 组件圆角，30px 按钮圆角，营造柔和视觉效果
- **轻柔阴影**：精心调配的阴影效果，增强层次感
- **充足留白**：合理的间距布局，提供舒适的浏览体验

## 🚀 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite
- **UI 组件库**：Element Plus
- **图标库**：@element-plus/icons-vue
- **路由管理**：Vue Router 4
- **状态管理**：Pinia
- **HTTP 客户端**：Axios
- **日期处理**：Day.js

## 📦 功能模块

### 用户系统
- 用户注册/登录
- 个人中心
- 用户信息管理

### 商品展示
- 商品列表展示
- 商品详情页面
- 分类浏览
- 商品搜索与筛选

### 购物功能
- 购物车管理
- 订单创建与管理
- 支付流程

### 管理后台
- 商品管理
- 订单管理
- 用户管理

## 🛠️ 开发指南

### 环境要求
- Node.js >= 16.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 📱 响应式设计

项目采用响应式设计，完美适配：
- 桌面端 (>= 1200px)
- 平板端 (768px - 1199px)
- 移动端 (< 768px)

## 🎨 设计规范

### 颜色规范
- 背景色：#f5f5f1 (淡米色)
- 主要文字：#4a593d (深绿色)
- 标题文字：#6e8b67 (中等绿色)
- 卡片背景：#fffcf6 (奶白色)
- 边框颜色：#e4ddd3 (淡棕色)
- 按钮颜色：#a9c3a6 (淡绿色)
- 按钮悬停：#8fb58b (较深绿色)

### 圆角规范
- 组件圆角：15px
- 按钮圆角：30px

## 📄 许可证

MIT License
