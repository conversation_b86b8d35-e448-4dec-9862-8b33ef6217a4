// 通用样式类

// 布局类
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-1 {
  flex: 1;
}

// 间距类
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

// 文字类
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-xxl { font-size: var(--font-size-xxl); }
.text-title { font-size: var(--font-size-title); }

.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-title-color { color: var(--color-text-title); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-bold { font-weight: bold; }
.text-medium { font-weight: 500; }
.text-normal { font-weight: normal; }

// 卡片样式
.card {
  background-color: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
}

.card-hover {
  background-color: var(--color-bg-card);
  border: 1px solid var(--color-border);
  border-radius: var(--border-radius-medium);
  box-shadow: var(--shadow-light);
  padding: var(--spacing-lg);
  transition: var(--transition-normal);
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
  }
}

// 按钮样式
.btn-natural {
  border-radius: var(--border-radius-large);
  font-weight: 500;
  transition: var(--transition-normal);
  cursor: pointer;
  border: none;
  outline: none;
  background-color: var(--color-button);
  color: white;
  border: 1px solid var(--color-button);
  padding: var(--spacing-sm) var(--spacing-lg);

  &:hover {
    background-color: var(--color-button-hover);
    border-color: var(--color-button-hover);
  }
}

.btn-outline {
  border-radius: var(--border-radius-large);
  font-weight: 500;
  transition: var(--transition-normal);
  cursor: pointer;
  border: none;
  outline: none;
  background-color: transparent;
  color: var(--color-button);
  border: 1px solid var(--color-button);
  padding: var(--spacing-sm) var(--spacing-lg);
  
  &:hover {
    background-color: var(--color-button);
    color: white;
  }
}

// 工具类
.w-full { width: 100%; }
.h-full { height: 100%; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }

.z-1 { z-index: 1; }
.z-10 { z-index: 10; }
.z-100 { z-index: 100; }
.z-1000 { z-index: 1000; }

// 显示/隐藏
.hidden { display: none; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// 圆角
.rounded-sm { border-radius: var(--border-radius-small); }
.rounded-md { border-radius: var(--border-radius-medium); }
.rounded-lg { border-radius: var(--border-radius-large); }
.rounded-full { border-radius: 50%; }

// 阴影
.shadow-light { box-shadow: var(--shadow-light); }
.shadow-medium { box-shadow: var(--shadow-medium); }
.shadow-heavy { box-shadow: var(--shadow-heavy); }

// 过渡动画
.transition-fast { transition: var(--transition-fast); }
.transition-normal { transition: var(--transition-normal); }
.transition-slow { transition: var(--transition-slow); }
