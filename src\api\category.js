import request from './request'

// 获取分类列表（管理员）
export const getAdminCategories = (params) => {
  return request({
    url: '/admin/categories',
    method: 'get',
    params
  })
}

// 获取分类详情（管理员）
export const getAdminCategoryDetail = (id) => {
  return request({
    url: `/admin/categories/${id}`,
    method: 'get'
  })
}

// 创建分类
export const createCategory = (data) => {
  return request({
    url: '/admin/categories',
    method: 'post',
    data
  })
}

// 更新分类
export const updateCategory = (id, data) => {
  return request({
    url: `/admin/categories/${id}`,
    method: 'put',
    data
  })
}

// 删除分类
export const deleteCategory = (id) => {
  return request({
    url: `/admin/categories/${id}`,
    method: 'delete'
  })
}

// 批量删除分类
export const batchDeleteCategories = (ids) => {
  return request({
    url: '/admin/categories/batch-delete',
    method: 'post',
    data: { ids }
  })
}

// 更新分类状态
export const updateCategoryStatus = (id, status) => {
  return request({
    url: `/admin/categories/${id}/status`,
    method: 'put',
    data: { status }
  })
}

// 更新分类排序
export const updateCategorySort = (id, sort) => {
  return request({
    url: `/admin/categories/${id}/sort`,
    method: 'put',
    data: { sort }
  })
}

// 获取分类树形结构
export const getCategoryTree = () => {
  return request({
    url: '/admin/categories/tree',
    method: 'get'
  })
}

// 移动分类（调整父级分类）
export const moveCategory = (id, parentId) => {
  return request({
    url: `/admin/categories/${id}/move`,
    method: 'put',
    data: { parentId }
  })
}

// 上传分类图片
export const uploadCategoryImage = (file) => {
  const formData = new FormData()
  formData.append('image', file)
  
  return request({
    url: '/upload/image',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}
