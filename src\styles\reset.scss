// CSS Reset
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  font-size: var(--font-size-md);
  line-height: var(--line-height-normal);
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 移除默认样式
ul, ol {
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
  
  &:hover {
    text-decoration: none;
  }
}

button {
  border: none;
  outline: none;
  cursor: pointer;
  background: transparent;
}

input, textarea, select {
  outline: none;
  border: none;
  font-family: inherit;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-bg-light);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: 4px;
  
  &:hover {
    background: var(--color-text-muted);
  }
}

// 选中文本样式
::selection {
  background-color: var(--color-button);
  color: white;
}

::-moz-selection {
  background-color: var(--color-button);
  color: white;
}
